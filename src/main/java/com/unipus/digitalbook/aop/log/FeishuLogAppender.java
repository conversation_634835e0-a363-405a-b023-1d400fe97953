package com.unipus.digitalbook.aop.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.Disposable;

import java.util.Map;

@Configuration
public class FeishuLogAppender extends AppenderBase<ILoggingEvent> {

    private static final String webhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/4a52098a-26e5-4f05-9ad3-331d75b1173c";
    private final WebClient webClient = WebClient.create();

    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!eventObject.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.ERROR)) {
            return;
        }

//        FeishuCard feishuCard = new FeishuCard("【异常日志】", eventObject.getFormattedMessage(), getStackTrace(eventObject)).toMap();
        Map<String, Object> card = FeishuCard.build(eventObject.getFormattedMessage(), getStackTrace(eventObject));
        Map<String, Object> body = Map.of(
                "msg_type", "text",
                "content", Map.of("text", "【异常告警】" + eventObject.getFormattedMessage())
        );

        Disposable subscribe = webClient.post()
                .uri(webhookUrl)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .subscribe();
        // 输出返回值，判断是否发送成功
        System.out.println("发送结果: " + subscribe.isDisposed());
    }

    private String getStackTrace(ILoggingEvent event) {
        if (event.getThrowableProxy() != null) {
            StringBuilder sb = new StringBuilder();
            sb.append(event.getThrowableProxy().getClassName()).append(": ")
                    .append(event.getThrowableProxy().getMessage()).append("\n");
            for (int i = 0; i < event.getThrowableProxy().getStackTraceElementProxyArray().length; i++) {
                sb.append("\t").append(event.getThrowableProxy().getStackTraceElementProxyArray()[i]).append("\n");
            }
            return sb.toString();
        }
        return "";
    }

    /**
     * 飞书 interactive 卡片实体
     */
    public static class FeishuCard {

        private final JSONObject card;

        public FeishuCard(String title, String message, String stackTrace) {
            JSONObject cardObj = new JSONObject();
            cardObj.put("msg_type", "interactive");

            JSONObject cardContent = new JSONObject();
            JSONObject config = new JSONObject();
            config.put("wide_screen_mode", true);
            cardContent.put("config", config);

            JSONObject header = new JSONObject();
            JSONObject headerTitle = new JSONObject();
            headerTitle.put("tag", "plain_text");
            headerTitle.put("content", title);
            header.put("title", headerTitle);
            header.put("template", "red");
            cardContent.put("header", header);

            JSONArray elements = new JSONArray();

            JSONObject messageElement = new JSONObject();
            messageElement.put("tag", "div");
            JSONObject messageText = new JSONObject();
            messageText.put("tag", "lark_md");
            messageText.put("content", "**【异常日志】**\n" + message);
            messageElement.put("text", messageText);
            elements.add(messageElement);

            JSONObject collapseElement = new JSONObject();
            collapseElement.put("tag", "collapse");
            collapseElement.put("collapsed", true);
            JSONObject collapseTitle = new JSONObject();
            collapseTitle.put("tag", "plain_text");
            collapseTitle.put("content", "展开异常堆栈");
            collapseElement.put("title", collapseTitle);

            JSONArray fields = new JSONArray();
            JSONObject field = new JSONObject();
            field.put("is_short", false);
            JSONObject fieldText = new JSONObject();
            fieldText.put("tag", "lark_md");
            fieldText.put("content", "```\n" + stackTrace + "\n```");
            field.put("text", fieldText);
            fields.add(field);
            collapseElement.put("fields", fields);
            elements.add(collapseElement);
            cardContent.put("elements", elements);
            cardObj.put("card", cardContent);

            this.card = cardObj;
        }

        public Map<String, Object> toMap() {
            System.out.println(card.toString());
            return card.getInnerMap();
        }

        public static Map<String, Object> build(String message, String stackTrace) {
            String body =
                """
                    {
                      "config": {
                        "wide_screen_mode": true
                      },
                      "header": {
                        "title": {
                          "tag": "plain_text",
                          "content": "【异常日志】"
                        },
                        "template": "red"
                      },
                      "elements": [
                        {
                          "tag": "div",
                          "text": {
                            "tag": "lark_md",
                            "content": "**【异常日志】**\\n<message>"
                          }
                        },
                        {
                          "tag": "collapse",
                          "title": {
                            "tag": "plain_text",
                            "content": "展开异常堆栈"
                          },
                          "collapsed": true,
                          "fields": [
                            {
                              "is_short": false,
                              "text": {
                                "tag": "lark_md",
                                "content": "```\\n<stackTrace>\\n```"
                              }
                            }
                          ]
                        }
                      ]
                    }
                """;


            return Map.of(
            "msg_type", "text",
            "content",  Map.of(
                    "text","【异常】服务出现 NullPointerException，请及时处理！"
                )
            );
        }

    }
}