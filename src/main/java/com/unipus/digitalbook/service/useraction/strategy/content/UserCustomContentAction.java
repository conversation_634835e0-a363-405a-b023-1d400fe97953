package com.unipus.digitalbook.service.useraction.strategy.content;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.dao.UserCustomContentProgressPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.ContentNode;
import com.unipus.digitalbook.model.entity.action.NodeProgress;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.action.UserContentProgressData;
import com.unipus.digitalbook.model.entity.content.CustomContentNode;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.events.UserActionEvent;
import com.unipus.digitalbook.model.po.action.UserCustomContentProgressPO;
import com.unipus.digitalbook.producer.TenantMessageProducer;
import com.unipus.digitalbook.publisher.UserActionPublisher;
import com.unipus.digitalbook.service.CustomContentService;
import com.unipus.digitalbook.service.useraction.strategy.node.NodeCompletionStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 自定义内容行为
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserCustomContentAction implements UserContentAction {

    @Resource
    private NodeCompletionStrategyFactory nodeCompletionStrategyFactory;
    @Resource
    private CustomContentService customContentService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private UserActionPublisher userActionPublisher;
    @Resource
    private UserCustomContentProgressPOMapper userCustomContentProgressPOMapper;
    @Resource
    private TenantMessageProducer tenantMessageProducer;

    @Override
    public Set<ContentTypeEnum> getTypes() {
        return Set.of(ContentTypeEnum.CUSTOM_CONTENT);
    }

    @Override
    public Response<Boolean> finishNode(UserAction userAction) {
        String bizId = userAction.getContentId();
        Map<String, CustomContentNode> nodeMap = customContentService.getNodeMapByBizId(bizId);
        if (nodeMap == null || nodeMap.isEmpty()) {
            log.debug("内容没有节点 bizId: {}", bizId);
            return Response.success(false);
        }
        String nodeId = userAction.getNodeId();
        CustomContentNode customContentNode = nodeMap.get(nodeId);
        if (customContentNode == null) {
            log.error("节点不存在, 或者已经变更 node = {}", nodeId);
            return Response.fail("节点不存在");
        }

        ContentNode node = new ContentNode(customContentNode);
        boolean completed = nodeCompletionStrategyFactory.getStrategy(node.getType()).isCompleted(node, userAction);
        if (!completed) {
            log.debug("节点未完成 {}", node.getId());
            return Response.success(false);
        }
        String openId = userAction.getOpenId();
        String envPartition = userAction.getEnvPartition();
        Long tenantId = userAction.getTenantId();
        setFinishNode(tenantId, openId, bizId, envPartition, node.getOffset());
        userActionPublisher.send(new UserActionEvent(node, userAction));
        return Response.success(true);
    }

    @Override
    public Response<Boolean> finishQuestionNode(UserAction userAction) {
        Map<String, CustomContentNode> nodeMap = customContentService.getQuestionNodeMapByBizId(userAction.getContentId());
        if (nodeMap == null || nodeMap.isEmpty()) {
            log.debug("内容没有节点 bizId: {}", userAction.getContentId());
            return Response.success(false);
        }
        CustomContentNode customContentNode = nodeMap.get(userAction.getQuestionId());
        if (customContentNode == null) {
            log.error("节点不存在, 或者已经变更 node = {}", userAction.getQuestionId());
            return Response.fail("节点不存在");
        }
        Long tenantId = userAction.getTenantId();
        String openId = userAction.getOpenId();
        String envPartition = userAction.getEnvPartition();
        String bizId = userAction.getContentId();
        ContentNode node = new ContentNode(customContentNode);
        setFinishNode(tenantId, openId, bizId, envPartition, node.getOffset());
        userActionPublisher.send(new UserActionEvent(node, userAction));
        return Response.success(true);
    }

    @Override
    public Response<Boolean> postProcessNode(UserAction userAction, ContentNode contentNode) {
        // 入库
        saveProgress(userAction);
        // 同步给第三方应用
        syncSingleNode(userAction, contentNode);
        return Response.success(true);
    }

    @Override
    public Response<Boolean> postPublishProcess(Long tenantId, String contentId, Long contentVersionId) {
        migratedProgress(tenantId, contentId);
        return Response.success(true);
    }

    private void syncSingleNode(UserAction userAction, ContentNode node) {
        // 跳过题题目进度同步，题目进度已经通过作答接口同步
        if (node.isRealQuestionNode()) {
            return;
        }
        log.info("sync content: {}", userAction.getContentId());
        if (Optional.ofNullable(tenantMessageProducer.getTenantSubscribe(
                userAction.getTenantId(), MessageTopicEnum.PUSH_USER_PROGRESS)).isPresent()) {
            Response<String> response = tenantMessageProducer.produceSyncMessage(
                    userAction.getTenantId(),
                    MessageTopicEnum.PUSH_USER_PROGRESS,
                    new TypeReference<>() {
                    },
                    new UserContentProgressData(userAction, new NodeProgress(node.getId(), node.getType(), 1, 1)),
                    new TypeReference<>() {});
            if (!response.isSuccess()) {
                log.error("同步进度失败  {}", response.getMessage());
            }
        } else {
            log.info("租户未订阅消息主题: {}, 租户ID: {}", MessageTopicEnum.PUSH_USER_PROGRESS.name(), userAction.getTenantId());
        }
    }
    private Response<Boolean> saveProgress(UserAction userAction) {
        Long tenantId = userAction.getTenantId();
        String openId = userAction.getOpenId();
        String bizId = userAction.getContentId();
        String envPartition = userAction.getEnvPartition();
        byte[] bit = getNodeProgress(tenantId, openId, bizId, envPartition);
        userCustomContentProgressPOMapper.saveProgressBit(tenantId, openId, bizId, envPartition, bit);
        return Response.success(true);
    }

    public Response<Boolean> migratedProgress(Long tenantId, String bizId) {
        Map<String, CustomContentNode> nodeMap = customContentService.getNodeMapByBizId(bizId);
        log.debug("migrate custom contentId: {}", bizId);
        // 获取章节下所有用户当前章节的进度数据
        // todo 迁移需要老的节点数据，但是这里更新后已经没有了，无法迁移学习记录，需要把老的数据做做缓存，然后再迁移
        return Response.success();
    }

    private void migrateUserProgress(Long tenantId, String openId, String contentId, Map<String, CustomContentNode> nodeMap) {
        log.debug("migrate user progress : {}, {}, {}", tenantId, openId, contentId);
    }

    private byte[] getNodeProgress(Long tenantId, String openId, String bizId, String envPartition) {
        String progressKey = getProgressKey(tenantId, openId, bizId, envPartition);
        byte[] progressRawKey = ((RedisSerializer<String>)stringRedisTemplate.getKeySerializer())
                .serialize(progressKey);
        if (!stringRedisTemplate.hasKey(progressKey)) {
            byte[] progressBit = userCustomContentProgressPOMapper.getProgressBit(tenantId, openId, bizId, envPartition);
            batchSetBit(progressBit, progressRawKey);
        }
        return stringRedisTemplate.execute((RedisCallback<byte[]>) connection ->
                connection.stringCommands().get(progressRawKey)
        );
    }

    private void batchSetBit(byte[] progressBit, byte[] progressRawKey) {
        if (progressBit == null || progressRawKey == null) return;
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            for (int byteIndex = 0; byteIndex < progressBit.length; byteIndex++) {
                byte b = progressBit[byteIndex];
                if (b == 0) continue;
                for (int bit = 0; bit < 8; bit++) {
                    boolean bitValue = ((b >> (7 - bit)) & 1) == 1;
                    if (!bitValue) continue;
                    int bitIndex = byteIndex * 8 + bit;
                    connection.stringCommands().setBit(
                            progressRawKey,
                            bitIndex,
                            true
                    );
                }
            }
            return null;
        });
    }
    private Response<Boolean> doFinishNode(ContentNode node, UserAction userAction) {
        boolean completed = nodeCompletionStrategyFactory.getStrategy(node.getType()).isCompleted(node, userAction);
        if (!completed) {
            log.debug("节点未完成 {}", node.getId());
            return Response.success(false);
        }
        setFinishNode(userAction.getTenantId(), userAction.getOpenId(), userAction.getContentId(), userAction.getEnvPartition(), node.getOffset());
        userActionPublisher.send(new UserActionEvent(node, userAction));
        return Response.success(true);
    }
    private Boolean setFinishNode(Long tenantId, String openId, String bizId, String envPartition, int offset) {
        String progressKey = getProgressKey(tenantId, openId, bizId, envPartition);
        stringRedisTemplate.opsForValue().setBit(progressKey, offset, true);
        stringRedisTemplate.expire(progressKey, Duration.ofDays(7));
        return true;
    }

    private String getProgressKey(Long tenantId, String openId, String bizId, String envPartition) {
        if (envPartition == null) {
            return String.format("progress:custom:%d:%s:%s", tenantId, openId, bizId);
        }
        return String.format("progress:custom:%d:%s:%s:%s", tenantId, openId, bizId, envPartition);
    }
}
