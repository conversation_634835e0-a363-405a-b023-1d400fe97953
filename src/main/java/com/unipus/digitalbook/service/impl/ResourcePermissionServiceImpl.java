package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.ResourcePermissionPOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.permission.ResourcePermission;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.params.book.PermissionSearchParam;
import com.unipus.digitalbook.model.params.book.ResourceUserSearchParam;
import com.unipus.digitalbook.model.po.book.ResourcePermissionPO;
import com.unipus.digitalbook.model.po.book.ResourceUserPO;
import com.unipus.digitalbook.publisher.standalone.ResourcePermitEventPublisher;
import com.unipus.digitalbook.service.ResourcePermissionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 教材权限服务实现类
 */
@Service
@Slf4j
public class ResourcePermissionServiceImpl implements ResourcePermissionService {

    @Resource
    private UserServiceImpl userService;

    @Resource
    private ResourcePermissionPOMapper resourcePermissionPOMapper;

    @Resource
    private ResourcePermitEventPublisher resourcePermitEventPublisher;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取平台所有待分配分享权限的用户列表
     * 资源已分享用户与资源所有者除外
     * @param param 检索条件
     * @return 资源用户列表
     */
    @Override
    public List<ResourceUser> getUsersToShare(ResourceUserSearchParam param) {
        Integer targetPermission = PermissionTypeEnum.SHARE.getCode() | PermissionTypeEnum.OWNER.getCode();
        return getNoPermissionUser(null, param.getResourceId(), targetPermission, param.getKeyword(), param.getPageParams());
    }

    /**
     * 获取教材共享用户列表
     *
     * @param param 检索条件
     * @return 用户id列表
     */
    @Override
    public List<ResourceUser> getSharedUsers(ResourceUserSearchParam param) {
        return getResourceUsers(null, param.getResourceId(), null, PermissionTypeEnum.SHARE, param.getKeyword());
    }

    /**
     * 获取平台所有教材待分配权限用户列表
     * @param orgId 组织id
     * @param resourceId 资源id
     * @param permission 权限类型
     * @param keyword 关键词
     * @param pageParams 分页参数
     * @return 用户信息列表
     */
    @Override
    public List<ResourceUser> getNoPermissionUser(
            @Nullable Long orgId, String resourceId, Integer permission, @Nullable String keyword, @Nullable PageParams pageParams) {
        if(!StringUtils.hasText(resourceId)) {
            log.error("资源ID不能为空");
            throw new IllegalArgumentException("资源ID不能为空");
        }
        if(permission == null){
            log.error("权限类型不能为空,资源ID[{}]", resourceId);
            throw new IllegalArgumentException("权限类型不能为空");
        }

        // 1. 取得有效用户列表
        List<UserInfo> UserInfos = userService.selectValidUsersByOrgAndKeyword(orgId, keyword, pageParams);
        if (CollectionUtils.isEmpty(UserInfos)) {
            return List.of();
        }

        // 2. 取得指定资源权限的用户列表
        List<ResourcePermissionPO> existingPermissions = resourcePermissionPOMapper.selectPermission(
                resourceId, null, null, permission);

        // 3. 提取具有现有权限的用户ID
        Set<Long> existUserIds = existingPermissions.stream()
                .map(ResourcePermissionPO::getUserId)
                .collect(Collectors.toSet());

        // 4. 过滤掉具有现有权限的用户
        List<UserInfo> targetUserInfos = UserInfos.stream().filter(u -> !existUserIds.contains(u.getId())).toList();
        if(CollectionUtils.isEmpty(targetUserInfos)){
            return List.of();
        }

        return targetUserInfos.stream().filter(u->!existUserIds.contains(u.getId())).map(ResourceUser::new).toList();

    }

    /**
     * 更新用户分享权限
     * @param resourceId 资源id
     * @param userIds 用户id列表
     * @param opUserId 操作用户id
     * @return 更新结果
     */
    @Override
    @Transactional
    public Boolean updateUserSharePermission(String resourceId, List<Long> userIds, Long opUserId){
        if(!StringUtils.hasText(resourceId)){
            log.warn("资源ID参数为空");
            throw new IllegalArgumentException("资源ID参数为空");
        }
        // 追加输入权限列表
        List<ResourcePermission> targetLit = new ArrayList<>();
        for (Long userId : userIds) {
            targetLit.add(new ResourcePermission(resourceId, ResourceTypeEnum.BOOK.getCode(), userId, PermissionTypeEnum.SHARE.getCode()));
        }
        List<Long> finalUserIds = CollectionUtils.isEmpty(userIds)? new ArrayList<>() : userIds;

        // 查询指定资源的指定权限
        List<ResourcePermissionPO> condition = List.of(ResourcePermissionPO.build(resourceId, PermissionTypeEnum.SHARE.getCode()));
        List<ResourcePermissionPO> existPermissions = resourcePermissionPOMapper.selectPermissions(condition);
        List<ResourcePermissionPO> removableTargetList = existPermissions.stream()
                .filter(p -> !finalUserIds.contains(p.getUserId()) )
                .toList();

        // 追加删除权限列表
        for (ResourcePermissionPO removableTarget : removableTargetList) {
            removableTarget.setPermission(~PermissionTypeEnum.SHARE.getCode());
            targetLit.add(removableTarget.toEntity());
        }

        return updateUserPermission(targetLit, opUserId) ;
    }

    /**
     * 更新/追加用户资源权限(用于权限分配)
     * ※需要主动提供删除对象列表，并设置对应的权限值，默认不会删除未指定权限列表
     * @param inputPermissions 权限参数(包含资源id, 用户id, 权限类型)
     * @param opUserId 操作用户id
     * @return 更新结果
     */
    @Override
    @Transactional
    public Boolean updateUserPermission(List<ResourcePermission> inputPermissions, Long opUserId) {
        if(CollectionUtils.isEmpty(inputPermissions)){
            log.warn("用户权限参数为空");
            return false;
        }

        // -- 查询历史权限列表 --
        // 构造权限数据查询条件列表(资源ID)
        List<ResourcePermissionPO> conditionPermissions = inputPermissions.stream()
                .map(p -> ResourcePermissionPO.build(p.getResourceId()))
                .toList();
        // 查询指定资源的权限
        List<ResourcePermissionPO> existPermissions = resourcePermissionPOMapper.selectPermissions(conditionPermissions);
        if(CollectionUtils.isEmpty(existPermissions)){
            existPermissions = new ArrayList<>();
        }

        // 保存旧的权限数据实体(发布变更事件用)
        List<ResourcePermission> oldPermissionData = existPermissions.stream().map(ResourcePermissionPO::toEntity).toList();

        // -- 生成权限更新/追加用数据列表 --
        List<ResourcePermissionPO> targetList = new ArrayList<>();
        for (ResourcePermission input : inputPermissions) {
            // 判断资源用户是否在已在权限列表中
            ResourcePermissionPO existingPermission = getExistingPermission(input, existPermissions);
            // 计算权限
            Integer permissionTypeCode = computePermission(input, existingPermission);
            log.debug("权限计算结果:资源ID[{}]，用户ID[{}]，旧权限值[{}]，新权限值[{}]，计算后权限值[{}]",
                    input.getResourceId(), input.getUserId(),
                    existingPermission!=null ? existingPermission.getPermission():null,
                    input.getPermissionType(), permissionTypeCode);

            // 构造权限数据
            ResourcePermissionPO permissionPO;
            if(existingPermission==null){
                // 创建数据权限数据
                permissionPO = ResourcePermissionPO.build( input.getResourceId(),
                        input.getResourceType(), input.getUserId(), permissionTypeCode,true, opUserId);
            }else {
                if (permissionTypeCode.equals(existingPermission.getPermission())) {
                    // 权限值未改变，跳过
                    continue;
                }else {
                    // 更新已存在的权限
                    permissionPO = existingPermission;
                    permissionPO.setPermission(permissionTypeCode);
                    permissionPO.setUpdateBy(opUserId);
                }
            }
            // 添加到目标列表
            targetList.add(permissionPO);
        }

        if(CollectionUtils.isEmpty(targetList)) {
            log.debug("权限列表为空，跳过更新");
            return true;
        }

        // 批量更新/追加权限
        int result = resourcePermissionPOMapper.batchInsertOrUpdatePermissions(targetList);
        if(result<=0) {
            log.error("更新用户权限失败");
            return false;
        }

        // 清除权限缓存
        clearPermissionCache(targetList.stream().map(ResourcePermissionPO::getResourceId).toList());

        // 发布资源权限变更事件
        List<ResourcePermission> newPermissionData = targetList.stream().map(ResourcePermissionPO::toEntity).toList();
        resourcePermitEventPublisher.publishPermitChangeEvent(oldPermissionData, newPermissionData, "updateUserPermission", opUserId);

        return true;
    }

    /**
     * 获取已存在的权限
     * @param input 用户权限参数 (包含资源id, 用户id, 权限类型)
     * @param existPermissions 已存在的权限列表
     * @return 已存在的权限
     */
    private static ResourcePermissionPO getExistingPermission(ResourcePermission input, List<ResourcePermissionPO> existPermissions) {
        return existPermissions.stream()
                .filter(p -> p.getResourceId().equals(input.getResourceId()) && p.getUserId().equals(input.getUserId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 计算权限
     * @param permission 用户权限参数 (包含资源id, 用户id, 权限类型)
     * @param existingPermission 已存在的权限
     * @return 计算后的权限
     */
    private Integer computePermission(ResourcePermission permission, ResourcePermissionPO existingPermission) {
        // 计算权限（优先使用指定的统一权限，如果未指定统一权限则使用个别权限）
        Integer newPermission = permission.getPermissionType();
        // 如果已存在权限，则根据权限处理方式进行权限计算
        if (existingPermission==null){
            // 新增
            return newPermission;
        }else{
            // 更新权限
            return PermissionTypeEnum.compute(existingPermission.getPermission(), newPermission);
        }
    }

    /**
     * 根据资源ID或用户ID获取权限信息列表
     * @param params 权限检索参数
     * @return 权限信息列表
     */
    @Override
    public List<ResourcePermission> getPermissions(List<PermissionSearchParam> params){
        // 构造权限数据查询条件列表(资源ID, 资源类型, 用户ID, 权限类型)
        List<ResourcePermissionPO> conditionPermissions = params.stream()
                .map(p -> ResourcePermissionPO.build(p.getResourceId(), p.getResourceType(), p.getUserId(), p.getPermissionTypeCode()))
                .toList();

        // 查询指定资源的指定权限
        List<ResourcePermissionPO> permissions = resourcePermissionPOMapper.selectPermissions(conditionPermissions);
        if(CollectionUtils.isEmpty(permissions)){
            return Collections.emptyList();
        }
        return permissions.stream().map(ResourcePermissionPO::toEntity).toList();
    }

    /**
     * 根据资源ID或用户ID获取权限信息列表
     * @param resourceIds 资源ID列表
     * @return 权限信息列表
     */
    @Override
    public List<ResourcePermission> getPermissionsByResourceIds(List<String> resourceIds){
        // 构造权限数据查询条件列表(资源ID, 资源类型, 用户ID, 权限类型)
        List<ResourcePermissionPO> conditionPermissions = resourceIds.stream()
                .map(ResourcePermissionPO::build)
                .toList();

        // 查询指定资源的指定权限
        List<ResourcePermissionPO> permissions = resourcePermissionPOMapper.selectPermissions(conditionPermissions);
        if(CollectionUtils.isEmpty(permissions)){
            return Collections.emptyList();
        }
        return permissions.stream().map(ResourcePermissionPO::toEntity).toList();
    }

    /**
     * 根据用户ID列表与权限类型获取权限信息列表
     * @param userIds 用户ID列表
     * @param permissionTypeEnum 权限类型
     * @return 权限信息列表
     */
    @Override
    public List<ResourcePermission> getPermissionsByUserIds(List<Long> userIds, @Nullable PermissionTypeEnum permissionTypeEnum){
        // 构造权限数据查询条件列表(资源ID, 资源类型, 用户ID, 权限类型)
        List<ResourcePermissionPO> conditionPermissions = userIds.stream()
               .map(userId -> ResourcePermissionPO.build(userId, permissionTypeEnum))
               .toList();

        // 查询指定资源的指定权限
        List<ResourcePermissionPO> permissions = resourcePermissionPOMapper.selectPermissions(conditionPermissions);
        if(CollectionUtils.isEmpty(permissions)){
            return Collections.emptyList();
        }
        return permissions.stream().map(ResourcePermissionPO::toEntity).toList();
    }

    /**
     * 取得资源的用户信息列表
     * @param orgId 组织id
     * @param resourceId 资源id
     * @param resourceType 资源类型 (教材/章节)
     * @param permissionType 权限类型: 阅读/编辑
     * @param keyword 关键字
     * @return 用户列表
     */
    @Override
    public List<ResourceUser> getResourceUsers(Long orgId, String resourceId, @Nullable ResourceTypeEnum resourceType,
                                               @Nullable PermissionTypeEnum permissionType, @Nullable String keyword){
        if(!StringUtils.hasText(resourceId)){
            throw new IllegalArgumentException("资源ID不能为空");
        }
        // 查询资源权限
        List<ResourceUserPO> resourceUserPOS = resourcePermissionPOMapper.selectUserByResourceId(
                resourceId, resourceType!=null ? resourceType.getCode() : null,
                permissionType!=null ? permissionType.getCode(): null,
                keyword);
        if(CollectionUtils.isEmpty(resourceUserPOS)){
            return Collections.emptyList();
        }
        List<Long> userIds = resourceUserPOS.stream().map(ResourceUserPO::getUserId).toList();

        // 查询已授权用户是否依然有效
        Set<Long> validUserIds = userService.checkUserValidityWithUserIdsAndOrgId(orgId, userIds);

        // 更新用户有效状态
        List<ResourceUser> resourceUserList = resourceUserPOS.stream().map(ResourceUserPO::toEntity).toList();
        resourceUserList.forEach(ru -> ru.setUserValid(validUserIds.contains(ru.getUserId())));
        return resourceUserList;
    }

    /**
     * 取得用户资源列表
     *
     * @param userId 用户ID
     * @param resourceType 资源类型 (教材/章节)
     * @param permissionType 权限类型: 阅读/编辑
     * @return 资源ID列表
     */
    @Override
    public List<String> getUserResources(Long userId, @Nullable ResourceTypeEnum resourceType, @Nullable PermissionTypeEnum permissionType) {
        if(userId==null){
            throw new IllegalArgumentException("用户ID不能为空");
        }
        // 查询权限
        List<ResourcePermissionPO> resourcePermissionPOS = resourcePermissionPOMapper.selectPermission(
                null,
                resourceType!=null ? resourceType.getCode(): null,
                userId,
                permissionType!=null ? permissionType.getCode(): null);
        if(CollectionUtils.isEmpty(resourcePermissionPOS)){
            return Collections.emptyList();
        }
        return resourcePermissionPOS.stream().map(ResourcePermissionPO::getResourceId).toList();
    }

    /**
     * 删除用户权限
     * @param resourcePermission 权限数据
     * @param opUserId 操作用户id
     */
    @Override
    public Boolean removeUserPermission(ResourcePermission resourcePermission, Long opUserId) {
        return resourcePermissionPOMapper.removePermission(ResourcePermissionPO.fromEntity(resourcePermission, opUserId)) > 0;
    }

    /**
     * 判断用户是否有指定资源的指定权限
     *
     * @param resourcePermission 权限数据
     * @return 是否拥有权限 true:有 false:无
     */
    @Override
    public Boolean hasResourcePermission(ResourcePermission resourcePermission) {
        String resourceId = resourcePermission.getResourceId();
        Long userId = resourcePermission.getUserId();
        Integer permissionType = resourcePermission.getPermissionType();
        if(!StringUtils.hasText(resourceId) || userId==null || permissionType==null){
            return false;
        }
        // 根据资源ID查询权限列表
        List<ResourcePermission> permissions = loadResourcePermissionByResourceId(resourceId);
        if(CollectionUtils.isEmpty(permissions)){
            return false;
        }
        // 判断用户是否有指定资源的指定权限
        Boolean hasPermission = permissions.stream().anyMatch(p ->
                p.getUserId().equals(userId) && PermissionTypeEnum.match(p.getPermissionType(), permissionType));
        if(!hasPermission){
            log.info("用户[{}]没有权限访问资源[{}]，权限类型[{}]", userId, resourceId, permissionType);
        }
        return hasPermission;
    }

    /**
     * 从缓存中获取资源权限列表（如果没有则查询数据库）
     * @param resourceId 资源ID
     * @return 用户权限列表
     */
    private List<ResourcePermission> loadResourcePermissionByResourceId(String resourceId){
        String redisKey = CacheConstant.REDIS_DATA_PERMISSION_KEY + resourceId;

        // 取得Redis缓存的用户数据权限
        Set<String> redisPermissionSet = stringRedisTemplate.opsForSet().members(redisKey);
        if(CollectionUtils.isNotEmpty(redisPermissionSet)){
            // 缓存中有数据权限，直接返回
            return redisPermissionSet.stream().map(p -> JsonUtil.parseObject(p, ResourcePermission.class)).toList();
        }

        // 缓存中没有数据权限，查询数据库
        List<ResourcePermissionPO> poList = resourcePermissionPOMapper.selectPermission(
                resourceId, null, null, null);
        if(CollectionUtils.isEmpty(poList)){
            // 数据库中没有数据权限，返回空
            return Collections.emptyList();
        }
        List<ResourcePermission> entityList = poList.stream().map(ResourcePermissionPO::toEntity).toList();

        // 缓存数据权限
        String[] permissions = entityList.stream().map(JsonUtil::toJsonString).toArray(String[]::new);
        stringRedisTemplate.opsForSet().add(redisKey, permissions);
        
        // 更新集合过期时间
        stringRedisTemplate.expire(redisKey, CacheConstant.REDIS_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        return entityList;
    }

    /**
     *  清除指定资源的权限缓存
     * @param resourceIds 资源ID列表
     */
    public void clearPermissionCache(List<String> resourceIds){
        if(CollectionUtils.isEmpty(resourceIds)){
            return;
        }
        for (String resourceId : resourceIds) {
            stringRedisTemplate.delete(CacheConstant.REDIS_DATA_PERMISSION_KEY + resourceId);
        }
    }
}
